/* Floating Popup Styles */
#linkedin-automation-floating-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 420px;
    height: 520px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    z-index: 999999;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
    display: none;
}

#linkedin-automation-floating-popup.show {
    display: block;
    animation: popupFadeIn 0.3s ease-out;
}

@keyframes popupFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

#linkedin-automation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999998;
    display: none;
}

#linkedin-automation-overlay.show {
    display: block;
}

/* Launch Button */
#linkedin-automation-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 999997;
    background: linear-gradient(135deg, #0077b5 0%, #005885 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 16px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(0, 119, 181, 0.3);
    transition: all 0.3s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

#linkedin-automation-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 119, 181, 0.4);
}

.automation-panel {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 200px;
}

.automation-panel button {
    background-color: #0077b5;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.automation-panel button:hover {
    background-color: #0073b1;
}

.automation-panel button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.automation-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #f3f6f8;
    text-align: center;
    margin-top: 4px;
}

.automation-status.running {
    background-color: #0a8a0a;
    color: white;
}

.automation-status.success {
    background-color: #0a8a0a;
    color: white;
}

.automation-status.warning {
    background-color: #f5a623;
    color: white;
}

.automation-status.error {
    background-color: #e74c3c;
    color: white;
}

/* Floating Popup Content Styles */
.floating-popup-content {
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.floating-popup-header {
    background: linear-gradient(135deg, #0077b5 0%, #005885 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 12px 12px 0 0;
}

.floating-popup-header h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.floating-popup-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.floating-popup-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.floating-popup-body {
    flex: 1;
    padding: 0;
    overflow-y: auto;
}

/* Import existing popup styles for floating popup */
.floating-popup-content .container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 0 0 12px 12px;
}

.floating-popup-content .container header {
    display: none; /* Hide original header since we have floating-popup-header */
}

/* Adjust existing styles for floating context */
.floating-popup-content .tabs {
    padding: 16px 20px 0;
}

.floating-popup-content .tab-content {
    padding: 16px 20px;
    flex: 1;
    overflow-y: auto;
}

.floating-popup-content footer {
    padding: 16px 20px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

/* Modal adjustments for floating popup */
.floating-popup-content .modal {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.floating-popup-content .modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 8px;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
}
